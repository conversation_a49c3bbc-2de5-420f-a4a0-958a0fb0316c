import { useCallback } from 'react';
import { ActionCreators } from 'redux-undo';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { updateFilter, updateAnnotations, resetFilters, resetViewer, initializeViewer } from '@/store/slices/viewersSlice';
import type { ViewersState, FilterState } from '@/store/slices/viewersSlice';
import { selectCanUndo, selectCanRedo } from '@/store/selectors';

export const useViewerFilters = (viewerType: keyof ViewersState) => {
  const dispatch = useAppDispatch();
  
  const viewer = useAppSelector(state => state.viewers.present[viewerType]);
  const canUndo = useAppSelector(selectCanUndo);
  const canRedo = useAppSelector(selectCanRedo);

  const updateFilterValue = useCallback((filter: keyof FilterState, value: number | boolean) => {
    dispatch(updateFilter({ viewer: viewerType, filter, value }));
  }, [dispatch, viewerType]);

  const updateViewerAnnotations = useCallback((annotations: any) => {
    dispatch(updateAnnotations({ viewer: viewerType, annotations }));
  }, [dispatch, viewerType]);

  const resetAllFilters = useCallback(() => {
    dispatch(resetFilters({ viewer: viewerType }));
  }, [dispatch, viewerType]);

  const resetAll = useCallback(() => {
    dispatch(resetViewer({ viewer: viewerType }));
  }, [dispatch, viewerType]);

  const initViewer = useCallback((data: any) => {
    dispatch(initializeViewer({ viewer: viewerType, data }));
  }, [dispatch, viewerType]);

  const undo = useCallback(() => {
    dispatch(ActionCreators.undo());
  }, [dispatch]);

  const redo = useCallback(() => {
    dispatch(ActionCreators.redo());
  }, [dispatch]);

  const undoAll = useCallback(() => {
    dispatch(ActionCreators.jumpToPast(0));
  }, [dispatch]);

  return {
    filters: viewer.filters,
    annotations: viewer.annotations,
    canUndo,
    canRedo,
    updateFilter: updateFilterValue,
    updateAnnotations: updateViewerAnnotations,
    resetFilters: resetAllFilters,
    resetAll,
    initViewer,
    undo,
    redo,
    undoAll,
  };
};
