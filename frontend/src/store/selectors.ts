import { createSelector } from '@reduxjs/toolkit';
import { RootState } from './index';
import { ViewersState } from './slices/viewersSlice';

const selectViewersState = (state: RootState) => state.viewers.present;

export const selectImageViewer = createSelector(
  [selectViewersState],
  (viewers: ViewersState) => viewers.image
);

export const selectStackViewer = createSelector(
  [selectViewersState],
  (viewers: ViewersState) => viewers.stack
);

export const selectVolumeViewer = createSelector(
  [selectViewersState],
  (viewers: ViewersState) => viewers.volume
);

export const selectImageFilters = createSelector(
  [selectImageViewer],
  (viewer) => viewer.filters
);

export const selectStackFilters = createSelector(
  [selectStackViewer],
  (viewer) => viewer.filters
);

export const selectVolumeFilters = createSelector(
  [selectVolumeViewer],
  (viewer) => viewer.filters
);

export const selectImageAnnotations = createSelector(
  [selectImageViewer],
  (viewer) => viewer.annotations
);

export const selectStackAnnotations = createSelector(
  [selectStackViewer],
  (viewer) => viewer.annotations
);

export const selectVolumeAnnotations = createSelector(
  [selectVolumeViewer],
  (viewer) => viewer.annotations
);

export const selectCanUndo = (state: RootState) => state.viewers.past.length > 0;
export const selectCanRedo = (state: RootState) => state.viewers.future.length > 0;
