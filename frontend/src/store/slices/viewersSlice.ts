import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface FilterState {
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
}

export interface ViewerState {
  filters: FilterState;
  annotations: any;
}

export interface ViewersState {
  image: ViewerState;
  stack: ViewerState;
  volume: ViewerState;
}

const defaultFilters: FilterState = {
  brightness: 1,
  contrast: 1,
  grayscale: false,
  invert: false,
  sharpness: 1,
  gammaR: 1,
  gammaG: 1,
  gammaB: 1,
};

const defaultViewer: ViewerState = {
  filters: defaultFilters,
  annotations: { version: "6.6.6", objects: [], background: "transparent" },
};

const initialState: ViewersState = {
  image: defaultViewer,
  stack: defaultViewer,
  volume: defaultViewer,
};

const viewersSlice = createSlice({
  name: 'viewers',
  initialState,
  reducers: {
    updateFilter: (
      state,
      action: PayloadAction<{
        viewer: keyof ViewersState;
        filter: keyof FilterState;
        value: number | boolean;
      }>
    ) => {
      const { viewer, filter, value } = action.payload;
      state[viewer].filters[filter] = value as any;
    },

    updateAnnotations: (
      state,
      action: PayloadAction<{
        viewer: keyof ViewersState;
        annotations: any;
      }>
    ) => {
      const { viewer, annotations } = action.payload;
      state[viewer].annotations = annotations;
    },

    resetFilters: (
      state,
      action: PayloadAction<{ viewer: keyof ViewersState }>
    ) => {
      const { viewer } = action.payload;
      state[viewer].filters = { ...defaultFilters };
    },

    resetViewer: (
      state,
      action: PayloadAction<{ viewer: keyof ViewersState }>
    ) => {
      const { viewer } = action.payload;
      state[viewer] = { ...defaultViewer };
    },

    initializeViewer: (
      state,
      action: PayloadAction<{
        viewer: keyof ViewersState;
        data: Partial<ViewerState>;
      }>
    ) => {
      const { viewer, data } = action.payload;
      state[viewer] = {
        filters: { ...defaultFilters, ...data.filters },
        annotations: data.annotations || defaultViewer.annotations,
      };
    },
  },
});

export const {
  updateFilter,
  updateAnnotations,
  resetFilters,
  resetViewer,
  initializeViewer,
} = viewersSlice.actions;

export default viewersSlice.reducer;
