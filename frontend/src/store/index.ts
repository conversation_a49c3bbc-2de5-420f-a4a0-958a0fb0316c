import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import undoable from 'redux-undo';
import viewersReducer from './slices/viewersSlice';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['viewers']
};

const persistedReducer = persistReducer(
  persistConfig,
  undoable(viewersReducer, {
    limit: 10,
    filter: (action) =>
      action.type.startsWith('viewers/updateFilter') ||
      action.type.startsWith('viewers/updateAnnotations'),
  })
);

export const store = configureStore({
  reducer: {
    viewers: persistedReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
