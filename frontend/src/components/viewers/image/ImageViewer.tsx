import { useEffect, useRef, useState } from 'react';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  setupImageCanvas,
  disposeFabricCanvas,
  applyImageFilters,
  setupFabricCanvas,
  rotateImageCanvas,
  flipImageCanvasHorizontal,
  flipImageCanvasVertical
} from '@/lib/fabric';
import { MedicalImageViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';

const ImageViewer: React.FC<MedicalImageViewerProps> = ({ data }) => {
  const imageCanvasRef = useRef<HTMLCanvasElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const isSetupInProgress = useRef<boolean>(false);

  const [brightness, setBrightness] = useState(data.viewer.fabricConfigs.brightness);
  const [contrast, setContrast] = useState(data.viewer.fabricConfigs.contrast);
  const [grayscale, setGrayscale] = useState(data.viewer.fabricConfigs.grayscale);
  const [invert, setInvert] = useState(data.viewer.fabricConfigs.invert);
  const [sharpness, setSharpness] = useState(data.viewer.fabricConfigs.sharpness);
  const [gamma, setGamma] = useState(data.viewer.fabricConfigs.gamma);

  useEffect(() => {
    const imageCanvas = imageCanvasRef.current;
    const fabricCanvasElement = fabricRef.current;
    if (!imageCanvas || !fabricCanvasElement) return;

    async function setupViewer() {
      if (isSetupInProgress.current) return;
      isSetupInProgress.current = true;

      if (imageCanvas) {
        await setupImageCanvas(imageCanvas, data.viewer.imageUrl);
        applyFilters();
      }

      if (fabricCanvasElement) {
        await setupFabricCanvas({
          fabricCanvasElement,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: true
        });
      }

      isSetupInProgress.current = false;
    }

    setupViewer();

    return () => {
      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!imageCanvasRef.current) return;
    applyImageFilters(imageCanvasRef.current, brightness, contrast, grayscale, invert, sharpness, gamma);
  };

  const handleBrightnessChange = (value: number) => {
    setBrightness(value);
    applyFilters();
  };

  const handleContrastChange = (value: number) => {
    setContrast(value);
    applyFilters();
  };

  const handleGrayscaleChange = (value: boolean) => {
    setGrayscale(value);
    if (!imageCanvasRef.current) return;
    applyImageFilters(imageCanvasRef.current, brightness, contrast, value, invert, sharpness, gamma);
  };

  const handleInvertChange = (value: boolean) => {
    setInvert(value);
    if (!imageCanvasRef.current) return;
    applyImageFilters(imageCanvasRef.current, brightness, contrast, grayscale, value, sharpness, gamma);
  };

  const handleSharpnessChange = (value: number) => {
    setSharpness(value);
    applyFilters();
  };

  const handleGammaChange = (value: number) => {
    setGamma(value);
    applyFilters();
  };

  const handleRotate = () => {
    if (!imageCanvasRef.current) return;
    rotateImageCanvas(imageCanvasRef.current);
  };

  const handleFlipHorizontal = () => {
    if (!imageCanvasRef.current) return;
    flipImageCanvasHorizontal(imageCanvasRef.current);
  };

  const handleFlipVertical = () => {
    if (!imageCanvasRef.current) return;
    flipImageCanvasVertical(imageCanvasRef.current);
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      brightness,
      contrast,
      grayscale,
      invert,
      sharpness,
      gamma,
      annotations: fabricCanvasData,
    }, 'image');
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div className="viewer-container">
        <canvas ref={imageCanvasRef} className="image-canvas" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gamma={gamma}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaChange={handleGammaChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onSave={handleSave}
        storageKey="fabric-annotations-image"
      />
    </div>
  );
};

export default ImageViewer;
