import { useEffect, useRef, useState } from 'react';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  setupImageCanvas,
  setupFabricImageCanvas,
  disposeFabricCanvas,
  applyFiltersToCanvas,
  setupFabricCanvas,
  rotateFabricCanvas,
  flipFabricCanvasHorizontal,
  flipFabricCanvasVertical
} from '@/lib/fabric';
import { MedicalImageViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';

const ImageViewer: React.FC<MedicalImageViewerProps> = ({ data }) => {
  const imageCanvasRef = useRef<HTMLCanvasElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const isSetupInProgress = useRef<boolean>(false);

  const [brightness, setBrightness] = useState(data.viewer.fabricConfigs.brightness);
  const [contrast, setContrast] = useState(data.viewer.fabricConfigs.contrast);
  const [grayscale, setGrayscale] = useState(data.viewer.fabricConfigs.grayscale);
  const [invert, setInvert] = useState(data.viewer.fabricConfigs.invert);
  const [sharpness, setSharpness] = useState(data.viewer.fabricConfigs.sharpness);
  const [gammaR, setGammaR] = useState(data.viewer.fabricConfigs.gammaR);
  const [gammaG, setGammaG] = useState(data.viewer.fabricConfigs.gammaG);
  const [gammaB, setGammaB] = useState(data.viewer.fabricConfigs.gammaB);

  const [undoStack, setUndoStack] = useState<any[]>([]);

  useEffect(() => {
    const imageCanvas = imageCanvasRef.current;
    const fabricCanvasElement = fabricRef.current;
    if (!imageCanvas || !fabricCanvasElement) return;

    async function setupViewer() {
      if (isSetupInProgress.current) return;
      isSetupInProgress.current = true;

      if (imageCanvas) {
        await setupImageCanvas(imageCanvas, data.viewer.imageUrl);
        applyFilters();
      }

      if (fabricCanvasElement) {
        await setupFabricCanvas({
          fabricCanvasElement,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: true
        });

        if (fabricCanvas.current) {
          await setupFabricImageCanvas(fabricCanvas.current, data.viewer.imageUrl);
          applyFilters();
        }
      }

      isSetupInProgress.current = false;
    }

    setupViewer();

    return () => {
      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!fabricCanvas.current) return;
    applyFiltersToCanvas(fabricCanvas.current, brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB);
  };

  const handleBrightnessChange = (value: number) => {
    saveToUndoStack();
    setBrightness(value);
    applyFilters();
  };

  const handleContrastChange = (value: number) => {
    saveToUndoStack();
    setContrast(value);
    applyFilters();
  };

  const handleGrayscaleChange = (value: boolean) => {
    saveToUndoStack();
    setGrayscale(value);
    if (!fabricCanvas.current) return;
    applyFiltersToCanvas(fabricCanvas.current, brightness, contrast, value, invert, sharpness, gammaR, gammaG, gammaB);
  };

  const handleInvertChange = (value: boolean) => {
    saveToUndoStack();
    setInvert(value);
    if (!fabricCanvas.current) return;
    applyFiltersToCanvas(fabricCanvas.current, brightness, contrast, grayscale, value, sharpness, gammaR, gammaG, gammaB);
  };

  const handleSharpnessChange = (value: number) => {
    saveToUndoStack();
    setSharpness(value);
    applyFilters();
  };

  const handleGammaRChange = (value: number) => {
    saveToUndoStack();
    setGammaR(value);
    applyFilters();
  };

  const handleGammaGChange = (value: number) => {
    saveToUndoStack();
    setGammaG(value);
    applyFilters();
  };

  const handleGammaBChange = (value: number) => {
    saveToUndoStack();
    setGammaB(value);
    applyFilters();
  };

  const saveToUndoStack = () => {
    const currentState = {
      brightness,
      contrast,
      grayscale,
      invert,
      sharpness,
      gammaR,
      gammaG,
      gammaB
    };
    setUndoStack(prev => [...prev.slice(-9), currentState]);
  };

  const handleUndo = () => {
    if (undoStack.length === 0) return;

    const lastState = undoStack[undoStack.length - 1];
    setUndoStack(prev => prev.slice(0, -1));

    setBrightness(lastState.brightness);
    setContrast(lastState.contrast);
    setGrayscale(lastState.grayscale);
    setInvert(lastState.invert);
    setSharpness(lastState.sharpness);
    setGammaR(lastState.gammaR);
    setGammaG(lastState.gammaG);
    setGammaB(lastState.gammaB);

    setTimeout(() => applyFilters(), 0);
  };

  const handleUndoAll = () => {
    if (undoStack.length > 0 || brightness !== data.viewer.fabricConfigs.brightness ||
        contrast !== data.viewer.fabricConfigs.contrast || grayscale !== data.viewer.fabricConfigs.grayscale ||
        invert !== data.viewer.fabricConfigs.invert || sharpness !== data.viewer.fabricConfigs.sharpness ||
        gammaR !== data.viewer.fabricConfigs.gammaR || gammaG !== data.viewer.fabricConfigs.gammaG ||
        gammaB !== data.viewer.fabricConfigs.gammaB) {

      setUndoStack([]);
      setBrightness(data.viewer.fabricConfigs.brightness);
      setContrast(data.viewer.fabricConfigs.contrast);
      setGrayscale(data.viewer.fabricConfigs.grayscale);
      setInvert(data.viewer.fabricConfigs.invert);
      setSharpness(data.viewer.fabricConfigs.sharpness);
      setGammaR(data.viewer.fabricConfigs.gammaR);
      setGammaG(data.viewer.fabricConfigs.gammaG);
      setGammaB(data.viewer.fabricConfigs.gammaB);

      setTimeout(() => applyFilters(), 0);
    }
  };

  const handleRotate = () => {
    if (!fabricCanvas.current) return;
    rotateFabricCanvas(fabricCanvas.current);
  };

  const handleFlipHorizontal = () => {
    if (!fabricCanvas.current) return;
    flipFabricCanvasHorizontal(fabricCanvas.current);
  };

  const handleFlipVertical = () => {
    if (!fabricCanvas.current) return;
    flipFabricCanvasVertical(fabricCanvas.current);
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      brightness,
      contrast,
      grayscale,
      invert,
      sharpness,
      gammaR,
      gammaG,
      gammaB,
      annotations: fabricCanvasData,
    }, 'image');
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div className="viewer-container">
        <canvas ref={imageCanvasRef} className="image-canvas" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        onUndoAll={handleUndoAll}
        onSave={handleSave}
        storageKey="fabric-annotations-image"
      />
    </div>
  );
};

export default ImageViewer;
