import { useEffect, useRef, useState } from 'react';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  setupImageCanvas,
  disposeFabricCanvas,
  applyImageFilters,
  setupFabricCanvas
} from '@/lib/fabric';
import { MedicalImageViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';

const ImageViewer: React.FC<MedicalImageViewerProps> = ({ data }) => {
  const imageCanvasRef = useRef<HTMLCanvasElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);

  const [brightness, setBrightness] = useState(data.viewer.fabricConfigs.brightness);
  const [contrast, setContrast] = useState(data.viewer.fabricConfigs.contrast);

  useEffect(() => {
    const imageCanvas = imageCanvasRef.current;
    const fabricCanvasElement = fabricRef.current;
    if (!imageCanvas || !fabricCanvasElement) return;

    async function setupViewer() {
      if (imageCanvas) {
        await setupImageCanvas(imageCanvas, data.viewer.imageUrl);
        applyFilters();
      }

      if (fabricCanvasElement) {
        await setupFabricCanvas({
          fabricCanvasElement,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: true // ImageViewer needs render delay
        });
      }
    }

    setupViewer();

    return () => {
      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!imageCanvasRef.current) return;
    applyImageFilters(imageCanvasRef.current, brightness, contrast);
  };

  const handleBrightnessChange = (value: number) => {
    setBrightness(value);
    applyFilters();
  };

  const handleContrastChange = (value: number) => {
    setContrast(value);
    applyFilters();
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      brightness,
      contrast,
      annotations: fabricCanvasData,
    }, 'image');
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div className="viewer-container">
        <canvas ref={imageCanvasRef} className="image-canvas" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onSave={handleSave}
        storageKey="fabric-annotations-image"
      />
    </div>
  );
};

export default ImageViewer;
