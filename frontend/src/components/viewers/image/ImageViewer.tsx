import { useEffect, useRef } from 'react';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  setupImageCanvas,
  setupFabricImageCanvas,
  disposeFabricCanvas,
  applyFiltersToCanvas,
  setupFabricCanvas,
  rotateFabricCanvas,
  flipFabricCanvasHorizontal,
  flipFabricCanvasVertical
} from '@/lib/fabric';
import { MedicalImageViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';
import { useViewerFilters } from '@/hooks/useViewerFilters';

const ImageViewer: React.FC<MedicalImageViewerProps> = ({ data }) => {
  const imageCanvasRef = useRef<HTMLCanvasElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const isSetupInProgress = useRef<boolean>(false);
  const eventDisposers = useRef<(() => void)[]>([]);

  const {
    filters,
    annotations,
    canUndo,
    updateFilter,
    updateAnnotations,
    undo,
    initViewer
  } = useViewerFilters('image');

  const { brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB } = filters;

  useEffect(() => {
    initViewer({
      filters: data.viewer.fabricConfigs,
      annotations: data.viewer.fabricConfigs.annotations
    });
  }, [data.id, initViewer]);

  useEffect(() => {
    if (fabricCanvas.current && annotations) {
      const canvas = fabricCanvas.current;

      eventDisposers.current.forEach(dispose => dispose());
      eventDisposers.current = [];

      canvas.loadFromJSON(annotations, () => {
        canvas.renderAll();

        const trackAnnotationChanges = () => {
          updateAnnotations(canvas.toJSON());
        };

        eventDisposers.current = [
          canvas.on('object:added', trackAnnotationChanges),
          canvas.on('object:removed', trackAnnotationChanges),
          canvas.on('object:modified', trackAnnotationChanges),
          canvas.on('path:created', trackAnnotationChanges)
        ];
      });

      applyFilters();
    }
  }, [annotations, updateAnnotations]);

  useEffect(() => {
    const imageCanvas = imageCanvasRef.current;
    const fabricCanvasElement = fabricRef.current;
    if (!imageCanvas || !fabricCanvasElement) return;

    async function setupViewer() {
      if (isSetupInProgress.current) return;
      isSetupInProgress.current = true;

      if (imageCanvas) {
        await setupImageCanvas(imageCanvas, data.viewer.imageUrl);
        applyFilters();
      }

      if (fabricCanvasElement) {
        await setupFabricCanvas({
          fabricCanvasElement,
          fabricCanvasRef: fabricCanvas,
          annotations: annotations,
          needsRenderDelay: true
        });

        if (fabricCanvas.current) {
          await setupFabricImageCanvas(fabricCanvas.current, data.viewer.imageUrl);
          applyFilters();
        }
      }

      isSetupInProgress.current = false;
    }

    setupViewer();

    return () => {
      eventDisposers.current.forEach(dispose => dispose());
      eventDisposers.current = [];

      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [data.viewer.imageUrl, annotations, updateAnnotations]);

  const applyFilters = () => {
    if (!fabricCanvas.current) return;
    applyFiltersToCanvas(fabricCanvas.current, brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB);
  };

  const handleBrightnessChange = (value: number) => {
    updateFilter('brightness', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleContrastChange = (value: number) => {
    updateFilter('contrast', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGrayscaleChange = (value: boolean) => {
    updateFilter('grayscale', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleInvertChange = (value: boolean) => {
    updateFilter('invert', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleSharpnessChange = (value: number) => {
    updateFilter('sharpness', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaRChange = (value: number) => {
    updateFilter('gammaR', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaGChange = (value: number) => {
    updateFilter('gammaG', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaBChange = (value: number) => {
    updateFilter('gammaB', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleUndo = () => {
    undo();
  };

  const handleRotate = () => {
    if (!fabricCanvas.current) return;
    rotateFabricCanvas(fabricCanvas.current);
  };

  const handleFlipHorizontal = () => {
    if (!fabricCanvas.current) return;
    flipFabricCanvasHorizontal(fabricCanvas.current);
  };

  const handleFlipVertical = () => {
    if (!fabricCanvas.current) return;
    flipFabricCanvasVertical(fabricCanvas.current);
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};
    updateAnnotations(fabricCanvasData);

    await saveFabricConfig(data.id, {
      ...filters,
      annotations: fabricCanvasData,
    }, 'image');
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div className="viewer-container">
        <canvas ref={imageCanvasRef} className="image-canvas" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={canUndo}
        onSave={handleSave}
        storageKey="fabric-annotations-image"
      />
    </div>
  );
};

export default ImageViewer;
