import { useEffect, useRef, useState } from 'react';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  setupImageCanvas,
  setupFabricImageCanvas,
  disposeFabricCanvas,
  applyFiltersToCanvas,
  setupFabricCanvas,
  rotateFabricCanvas,
  flipFabricCanvasHorizontal,
  flipFabricCanvasVertical
} from '@/lib/fabric';
import { MedicalImageViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';
import { useViewerFilters } from '@/hooks/useViewerFilters';

const ImageViewer: React.FC<MedicalImageViewerProps> = ({ data }) => {
  const imageCanvasRef = useRef<HTMLCanvasElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const isSetupInProgress = useRef<boolean>(false);
  const eventDisposers = useRef<(() => void)[]>([]);

  const {
    filters,
    updateFilter,
    initViewer
  } = useViewerFilters('image');

  const { brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB } = filters;

  const isUndoing = useRef(false);
  const [undoStack, setUndoStack] = useState<any[]>([]);
  const initialObjectCount = useRef(0);

  useEffect(() => {
    initViewer({
      filters: data.viewer.fabricConfigs,
      annotations: data.viewer.fabricConfigs.annotations
    });
  }, [data.id, initViewer]);



  useEffect(() => {
    const imageCanvas = imageCanvasRef.current;
    const fabricCanvasElement = fabricRef.current;
    if (!imageCanvas || !fabricCanvasElement) return;

    async function setupViewer() {
      if (isSetupInProgress.current) return;
      isSetupInProgress.current = true;

      if (imageCanvas) {
        await setupImageCanvas(imageCanvas, data.viewer.imageUrl);
        applyFilters();
      }

      if (fabricCanvasElement) {
        await setupFabricCanvas({
          fabricCanvasElement,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: true
        });

        if (fabricCanvas.current) {
          await setupFabricImageCanvas(fabricCanvas.current, data.viewer.imageUrl);
          applyFilters();

          const canvas = fabricCanvas.current;

          // Store initial object count (saved annotations) after a longer delay
          setTimeout(() => {
            initialObjectCount.current = canvas.getObjects().length;
          }, 1000);

          const trackAnnotationAdd = () => {
            if (!isUndoing.current && canvas) {
              setTimeout(() => {
                const allObjects = canvas.getObjects();
                const newObjectsCount = allObjects.length - initialObjectCount.current;
                if (newObjectsCount > 0) {
                  // Just track that we added an object
                  setUndoStack(prev => [...prev.slice(-9), { type: 'add', objectCount: allObjects.length }]);
                }
              }, 100);
            }
          };

          const trackAnnotationRemove = () => {
            if (!isUndoing.current && canvas) {
              setTimeout(() => {
                const allObjects = canvas.getObjects();
                setUndoStack(prev => [...prev.slice(-9), { type: 'remove', objectCount: allObjects.length }]);
              }, 100);
            }
          };

          // Clear any existing event listeners first
          eventDisposers.current.forEach(dispose => dispose());
          eventDisposers.current = [];

          eventDisposers.current = [
            canvas.on('path:created', trackAnnotationAdd),
            canvas.on('object:removed', trackAnnotationRemove),
            canvas.on('object:modified', trackAnnotationAdd)
          ];
        }
      }

      isSetupInProgress.current = false;
    }

    setupViewer();

    return () => {
      eventDisposers.current.forEach(dispose => dispose());
      eventDisposers.current = [];

      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!fabricCanvas.current) return;
    applyFiltersToCanvas(fabricCanvas.current, brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB);
  };

  const saveFilterState = () => {
    if (!fabricCanvas.current) return;
    const currentState = {
      type: 'filter',
      filters: { brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB },
      annotations: fabricCanvas.current.toJSON()
    };
    setUndoStack(prev => [...prev.slice(-9), currentState]);
  };

  const handleBrightnessChange = (value: number) => {
    saveFilterState();
    updateFilter('brightness', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleContrastChange = (value: number) => {
    saveFilterState();
    updateFilter('contrast', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGrayscaleChange = (value: boolean) => {
    // Save current state for undo BEFORE change
    saveFilterState();
    // Update the filter
    updateFilter('grayscale', value);
    // Apply filters immediately with the new value
    setTimeout(() => applyFilters(), 0);
  };

  const handleInvertChange = (value: boolean) => {
    // Save current state for undo BEFORE change
    saveFilterState();
    // Update the filter
    updateFilter('invert', value);
    // Apply filters immediately with the new value
    setTimeout(() => applyFilters(), 0);
  };

  const handleSharpnessChange = (value: number) => {
    saveFilterState();
    updateFilter('sharpness', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaRChange = (value: number) => {
    saveFilterState();
    updateFilter('gammaR', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaGChange = (value: number) => {
    saveFilterState();
    updateFilter('gammaG', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaBChange = (value: number) => {
    saveFilterState();
    updateFilter('gammaB', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleUndo = () => {
    if (!fabricCanvas.current || undoStack.length === 0) return;

    const canvas = fabricCanvas.current;
    const lastAction = undoStack[undoStack.length - 1];

    isUndoing.current = true;

    if (lastAction.type === 'add') {
      // Remove the last added object
      const objects = canvas.getObjects();
      if (objects.length > initialObjectCount.current) {
        const lastObject = objects[objects.length - 1];
        canvas.remove(lastObject);
        canvas.renderAll();
      }
    } else if (lastAction.type === 'filter') {
      // Restore filters
      const filters = lastAction.filters;
      updateFilter('brightness', filters.brightness);
      updateFilter('contrast', filters.contrast);
      updateFilter('grayscale', filters.grayscale);
      updateFilter('invert', filters.invert);
      updateFilter('sharpness', filters.sharpness);
      updateFilter('gammaR', filters.gammaR);
      updateFilter('gammaG', filters.gammaG);
      updateFilter('gammaB', filters.gammaB);
      setTimeout(() => applyFilters(), 50);
    } else if (lastAction.type === 'rotate') {
      // Undo rotate by rotating back 3 times (270 degrees)
      rotateFabricCanvas(canvas);
      rotateFabricCanvas(canvas);
      rotateFabricCanvas(canvas);

      // Also undo HTML5 canvas rotation
      if (imageCanvasRef.current) {
        const imageCanvas = imageCanvasRef.current;
        const currentTransform = imageCanvas.style.transform || '';
        const rotateMatch = currentTransform.match(/rotate\((-?\d+)deg\)/);
        const currentRotation = rotateMatch ? parseInt(rotateMatch[1]) : 0;
        const newRotation = (currentRotation - 90) % 360;

        const otherTransforms = currentTransform.replace(/rotate\(-?\d+deg\)\s?/, '').trim();
        imageCanvas.style.transform = `${otherTransforms} rotate(${newRotation}deg)`.trim();
      }
    } else if (lastAction.type === 'flipH') {
      // Undo horizontal flip by flipping again
      flipFabricCanvasHorizontal(canvas);
    } else if (lastAction.type === 'flipV') {
      // Undo vertical flip by flipping again
      flipFabricCanvasVertical(canvas);
    }

    // Remove the last action from undo stack
    setUndoStack(prev => prev.slice(0, -1));

    setTimeout(() => {
      isUndoing.current = false;
    }, 100);
  };

  const handleRotate = () => {
    if (!fabricCanvas.current || !imageCanvasRef.current) return;

    // Save state for undo
    setUndoStack(prev => [...prev.slice(-9), { type: 'rotate' }]);

    // Rotate the Fabric.js canvas background
    rotateFabricCanvas(fabricCanvas.current);

    // Also rotate the HTML5 canvas to prevent duplicate
    const imageCanvas = imageCanvasRef.current;
    const currentTransform = imageCanvas.style.transform || '';
    const rotateMatch = currentTransform.match(/rotate\((-?\d+)deg\)/);
    const currentRotation = rotateMatch ? parseInt(rotateMatch[1]) : 0;
    const newRotation = (currentRotation + 90) % 360;

    const otherTransforms = currentTransform.replace(/rotate\(-?\d+deg\)\s?/, '').trim();
    imageCanvas.style.transform = `${otherTransforms} rotate(${newRotation}deg)`.trim();
  };

  const handleFlipHorizontal = () => {
    if (!fabricCanvas.current) return;
    // Save state for undo
    setUndoStack(prev => [...prev.slice(-9), { type: 'flipH' }]);
    flipFabricCanvasHorizontal(fabricCanvas.current);
  };

  const handleFlipVertical = () => {
    if (!fabricCanvas.current) return;
    // Save state for undo
    setUndoStack(prev => [...prev.slice(-9), { type: 'flipV' }]);
    flipFabricCanvasVertical(fabricCanvas.current);
  };



  const handleShapeCreated = () => {
    // Track shape creation for undo
    setUndoStack(prev => [...prev.slice(-9), { type: 'add' }]);
  };

  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      ...filters,
      annotations: fabricCanvasData,
    }, 'image');
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div className="viewer-container">
        <canvas ref={imageCanvasRef} className="image-canvas" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={undoStack.length > 0}
        onSave={handleSave}
        onShapeCreated={handleShapeCreated}
        storageKey="fabric-annotations-image"
      />
    </div>
  );
};

export default ImageViewer;
