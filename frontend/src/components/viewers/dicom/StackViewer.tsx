import { useEffect, useRef, useState } from 'react';
import { Types } from '@cornerstonejs/core';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
} from '@/lib/dicom';
import {
  disposeFabricCanvas,
  applyCSSFilters,
  setupFabricCanvas,
  rotateDicomElement,
  flipDicomElementHorizontal,
  flipDicomElementVertical
} from '@/lib/fabric';
import { DicomStackViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';
import { useViewerFilters } from '@/hooks/useViewerFilters';

const StackViewer: React.FC<DicomStackViewerProps> = ({ data }) => {
  const cornerstoneRef = useRef<HTMLDivElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const eventDisposers = useRef<(() => void)[]>([]);

  const {
    filters,
    updateFilter,
    initViewer
  } = useViewerFilters('stack');

  const { brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB } = filters;

  const isUndoing = useRef(false);
  const [undoStack, setUndoStack] = useState<any[]>([]);
  const initialObjectCount = useRef(0);

  useEffect(() => {
    initViewer({
      filters: data.viewer.fabricConfigs,
      annotations: data.viewer.fabricConfigs.annotations
    });
  }, [data.id, initViewer]);



  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      if (renderingEngineRef.current) {
        renderingEngineRef.current.destroy();
        renderingEngineRef.current = null;
      }
      if (fabricCanvas.current) {
        fabricCanvas.current.dispose();
        fabricCanvas.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !cornerstoneRef.current) return;

    const element = cornerstoneRef.current;

    async function setupViewer() {
      const renderingEngineId = `stackViewer_${Date.now()}`;
      const viewportId = `viewport_${Date.now()}`;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;

      const viewport = setup2dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      await loadDicomStack(viewport, data.viewer.imageUrl);

      if (fabricRef.current) {
        await setupFabricCanvas({
          fabricCanvasElement: fabricRef.current,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: false
        });

        if (fabricCanvas.current) {
          const canvas = fabricCanvas.current;

          // Store initial object count (saved annotations)
          setTimeout(() => {
            initialObjectCount.current = canvas.getObjects().length;
          }, 500);

          const trackAnnotationAdd = () => {
            if (!isUndoing.current && canvas) {
              setTimeout(() => {
                const allObjects = canvas.getObjects();
                const newObjectsCount = allObjects.length - initialObjectCount.current;
                if (newObjectsCount > 0) {
                  setUndoStack(prev => [...prev.slice(-9), { type: 'add', objectCount: allObjects.length }]);
                }
              }, 100);
            }
          };

          eventDisposers.current = [
            canvas.on('path:created', trackAnnotationAdd),
            canvas.on('object:removed', trackAnnotationAdd),
            canvas.on('object:modified', trackAnnotationAdd)
          ];
        }
      }

      applyFilters();
    }

    setupViewer();

    return () => {
      eventDisposers.current.forEach(dispose => dispose());
      eventDisposers.current = [];

      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [isInitialized, data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!cornerstoneRef.current) return;
    applyCSSFilters(cornerstoneRef.current, brightness, contrast, grayscale, invert, sharpness);
  };

  const handleUndo = () => {
    if (!fabricCanvas.current || undoStack.length === 0) return;

    const canvas = fabricCanvas.current;
    const lastAction = undoStack[undoStack.length - 1];

    isUndoing.current = true;

    if (lastAction.type === 'add') {
      // Remove the last added object
      const objects = canvas.getObjects();
      if (objects.length > initialObjectCount.current) {
        const lastObject = objects[objects.length - 1];
        canvas.remove(lastObject);
        canvas.renderAll();
      }
    } else if (lastAction.type === 'filter') {
      // Restore filters
      const filters = lastAction.filters;
      updateFilter('brightness', filters.brightness);
      updateFilter('contrast', filters.contrast);
      updateFilter('grayscale', filters.grayscale);
      updateFilter('invert', filters.invert);
      updateFilter('sharpness', filters.sharpness);
      updateFilter('gammaR', filters.gammaR);
      updateFilter('gammaG', filters.gammaG);
      updateFilter('gammaB', filters.gammaB);
      setTimeout(() => applyFilters(), 50);
    }

    // Remove the last action from undo stack
    setUndoStack(prev => prev.slice(0, -1));

    setTimeout(() => {
      isUndoing.current = false;
    }, 100);
  };



  const saveFilterState = () => {
    if (!fabricCanvas.current) return;
    const currentState = {
      type: 'filter',
      filters: { brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB },
      annotations: fabricCanvas.current.toJSON()
    };
    setUndoStack(prev => [...prev.slice(-9), currentState]);
  };

  const handleBrightnessChange = (value: number) => {
    saveFilterState();
    updateFilter('brightness', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleContrastChange = (value: number) => {
    saveFilterState();
    updateFilter('contrast', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGrayscaleChange = (value: boolean) => {
    saveFilterState();
    updateFilter('grayscale', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleInvertChange = (value: boolean) => {
    saveFilterState();
    updateFilter('invert', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleSharpnessChange = (value: number) => {
    saveFilterState();
    updateFilter('sharpness', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaRChange = (value: number) => {
    saveFilterState();
    updateFilter('gammaR', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaGChange = (value: number) => {
    saveFilterState();
    updateFilter('gammaG', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaBChange = (value: number) => {
    saveFilterState();
    updateFilter('gammaB', value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleRotate = () => {
    if (!cornerstoneRef.current) return;
    rotateDicomElement(cornerstoneRef.current);
  };

  const handleFlipHorizontal = () => {
    if (!cornerstoneRef.current) return;
    flipDicomElementHorizontal(cornerstoneRef.current);
  };

  const handleFlipVertical = () => {
    if (!cornerstoneRef.current) return;
    flipDicomElementVertical(cornerstoneRef.current);
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      ...filters,
      annotations: fabricCanvasData,
    });
  };

  return (
    <div className="stack-viewer" id="stack-viewer-container">
      <div className="viewer-container">
        <div ref={cornerstoneRef} className="cornerstone-element" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={undoStack.length > 0}
        onSave={handleSave}
        storageKey="fabric-annotations-stack"
        disableGrayscale={true}
        disableGamma={true}
      />
    </div>
  );
};

export default StackViewer;
