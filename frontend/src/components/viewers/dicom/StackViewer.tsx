import { useEffect, useRef, useState } from 'react';
import { Types } from '@cornerstonejs/core';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
} from '@/lib/dicom';
import {
  disposeFabricCanvas,
  applyCSSFilters,
  setupFabricCanvas,
  rotateDicomElement,
  flipDicomElementHorizontal,
  flipDicomElementVertical
} from '@/lib/fabric';
import { DicomStackViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';

const StackViewer: React.FC<DicomStackViewerProps> = ({ data }) => {
  const cornerstoneRef = useRef<HTMLDivElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const eventDisposers = useRef<(() => void)[]>([]);
  const [brightness, setBrightness] = useState(data.viewer.fabricConfigs.brightness);
  const [contrast, setContrast] = useState(data.viewer.fabricConfigs.contrast);
  const [grayscale, setGrayscale] = useState(data.viewer.fabricConfigs.grayscale);
  const [invert, setInvert] = useState(data.viewer.fabricConfigs.invert);
  const [sharpness, setSharpness] = useState(data.viewer.fabricConfigs.sharpness);
  const [gammaR, setGammaR] = useState(data.viewer.fabricConfigs.gammaR);
  const [gammaG, setGammaG] = useState(data.viewer.fabricConfigs.gammaG);
  const [gammaB, setGammaB] = useState(data.viewer.fabricConfigs.gammaB);

  const [undoStack, setUndoStack] = useState<any[]>([]);

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      if (renderingEngineRef.current) {
        renderingEngineRef.current.destroy();
        renderingEngineRef.current = null;
      }
      if (fabricCanvas.current) {
        fabricCanvas.current.dispose();
        fabricCanvas.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !cornerstoneRef.current) return;

    const element = cornerstoneRef.current;

    async function setupViewer() {
      const renderingEngineId = `stackViewer_${Date.now()}`;
      const viewportId = `viewport_${Date.now()}`;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;

      const viewport = setup2dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      await loadDicomStack(viewport, data.viewer.imageUrl);

      if (fabricRef.current) {
        await setupFabricCanvas({
          fabricCanvasElement: fabricRef.current,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: false
        });

        if (fabricCanvas.current) {
          const canvas = fabricCanvas.current;
          const trackAnnotationChanges = () => {
            setUndoStack(prev => [...prev.slice(-9), {
              brightness,
              contrast,
              grayscale,
              invert,
              sharpness,
              annotations: canvas.toJSON()
            }]);
          };

          eventDisposers.current = [
            canvas.on('object:added', trackAnnotationChanges),
            canvas.on('object:removed', trackAnnotationChanges),
            canvas.on('object:modified', trackAnnotationChanges),
            canvas.on('path:created', trackAnnotationChanges)
          ];
        }
      }

      applyFilters();
    }

    setupViewer();

    return () => {
      eventDisposers.current.forEach(dispose => dispose());
      eventDisposers.current = [];

      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [isInitialized, data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!cornerstoneRef.current) return;
    applyCSSFilters(cornerstoneRef.current, brightness, contrast, grayscale, invert, sharpness);
  };

  const saveToUndoStack = () => {
    const currentState = {
      brightness,
      contrast,
      grayscale,
      invert,
      sharpness,
      annotations: fabricCanvas.current?.toJSON() || {}
    };
    setUndoStack(prev => [...prev.slice(-9), currentState]);
  };

  const handleUndo = () => {
    if (undoStack.length === 0) return;

    const lastState = undoStack[undoStack.length - 1];
    setUndoStack(prev => prev.slice(0, -1));

    setBrightness(lastState.brightness);
    setContrast(lastState.contrast);
    setGrayscale(lastState.grayscale);
    setInvert(lastState.invert);
    setSharpness(lastState.sharpness);

    if (fabricCanvas.current && lastState.annotations) {
      fabricCanvas.current.loadFromJSON(lastState.annotations, () => {
        fabricCanvas.current?.renderAll();
      });
    }

    setTimeout(() => applyFilters(), 0);
  };



  const handleBrightnessChange = (value: number) => {
    saveToUndoStack();
    setBrightness(value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleContrastChange = (value: number) => {
    saveToUndoStack();
    setContrast(value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGrayscaleChange = (value: boolean) => {
    saveToUndoStack();
    setGrayscale(value);
    setTimeout(() => {
      if (!cornerstoneRef.current) return;
      applyCSSFilters(cornerstoneRef.current, brightness, contrast, value, invert, sharpness);
    }, 0);
  };

  const handleInvertChange = (value: boolean) => {
    saveToUndoStack();
    setInvert(value);
    setTimeout(() => {
      if (!cornerstoneRef.current) return;
      applyCSSFilters(cornerstoneRef.current, brightness, contrast, grayscale, value, sharpness);
    }, 0);
  };

  const handleSharpnessChange = (value: number) => {
    saveToUndoStack();
    setSharpness(value);
    setTimeout(() => applyFilters(), 0);
  };

  const handleGammaRChange = (value: number) => {
    setGammaR(value);
    applyFilters();
  };

  const handleGammaGChange = (value: number) => {
    setGammaG(value);
    applyFilters();
  };

  const handleGammaBChange = (value: number) => {
    setGammaB(value);
    applyFilters();
  };

  const handleRotate = () => {
    if (!cornerstoneRef.current) return;
    rotateDicomElement(cornerstoneRef.current);
  };

  const handleFlipHorizontal = () => {
    if (!cornerstoneRef.current) return;
    flipDicomElementHorizontal(cornerstoneRef.current);
  };

  const handleFlipVertical = () => {
    if (!cornerstoneRef.current) return;
    flipDicomElementVertical(cornerstoneRef.current);
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      brightness,
      contrast,
      grayscale,
      invert,
      sharpness,
      gammaR,
      gammaG,
      gammaB,
      annotations: fabricCanvasData,
    });
  };

  return (
    <div className="stack-viewer" id="stack-viewer-container">
      <div className="viewer-container">
        <div ref={cornerstoneRef} className="cornerstone-element" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={undoStack.length > 0}
        onSave={handleSave}
        storageKey="fabric-annotations-stack"
        disableGrayscale={true}
        disableGamma={true}
      />
    </div>
  );
};

export default StackViewer;
