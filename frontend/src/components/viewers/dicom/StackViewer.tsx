import { useEffect, useRef, useState } from 'react';
import { Types } from '@cornerstonejs/core';
import { Canvas } from 'fabric';
import FabricToolbar from '@/components/toolbars/FabricToolbar';
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
} from '@/lib/dicom';
import {
  disposeFabricCanvas,
  applyImageFilters,
  setupFabricCanvas,
  rotateDicomElement,
  flipDicomElementHorizontal,
  flipDicomElementVertical
} from '@/lib/fabric';
import { DicomStackViewerProps } from '@/shared/types';
import { saveFabricConfig } from '@/shared/api';

const StackViewer: React.FC<DicomStackViewerProps> = ({ data }) => {
  const cornerstoneRef = useRef<HTMLDivElement>(null);
  const fabricRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<Canvas | null>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [brightness, setBrightness] = useState(data.viewer.fabricConfigs.brightness);
  const [contrast, setContrast] = useState(data.viewer.fabricConfigs.contrast);
  const [grayscale, setGrayscale] = useState(data.viewer.fabricConfigs.grayscale);
  const [invert, setInvert] = useState(data.viewer.fabricConfigs.invert);
  const [sharpness, setSharpness] = useState(data.viewer.fabricConfigs.sharpness);
  const [gammaR, setGammaR] = useState(data.viewer.fabricConfigs.gammaR);
  const [gammaG, setGammaG] = useState(data.viewer.fabricConfigs.gammaG);
  const [gammaB, setGammaB] = useState(data.viewer.fabricConfigs.gammaB);

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      if (renderingEngineRef.current) {
        renderingEngineRef.current.destroy();
        renderingEngineRef.current = null;
      }
      if (fabricCanvas.current) {
        fabricCanvas.current.dispose();
        fabricCanvas.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !cornerstoneRef.current) return;

    const element = cornerstoneRef.current;

    async function setupViewer() {
      const renderingEngineId = `stackViewer_${Date.now()}`;
      const viewportId = `viewport_${Date.now()}`;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;

      const viewport = setup2dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      await loadDicomStack(viewport, data.viewer.imageUrl);

      if (fabricRef.current) {
        await setupFabricCanvas({
          fabricCanvasElement: fabricRef.current,
          fabricCanvasRef: fabricCanvas,
          annotations: data.viewer.fabricConfigs.annotations,
          needsRenderDelay: false
        });
      }

      applyFilters();
    }

    setupViewer();

    return () => {
      if (fabricCanvas.current) {
        disposeFabricCanvas(fabricCanvas.current);
        fabricCanvas.current = null;
      }
    };
  }, [isInitialized, data.viewer.imageUrl]);

  const applyFilters = () => {
    if (!cornerstoneRef.current) return;
    applyImageFilters(cornerstoneRef.current, brightness, contrast, grayscale, invert, sharpness, gammaR, gammaG, gammaB);
  };

  const handleBrightnessChange = (value: number) => {
    setBrightness(value);
    applyFilters();
  };

  const handleContrastChange = (value: number) => {
    setContrast(value);
    applyFilters();
  };

  const handleGrayscaleChange = (value: boolean) => {
    setGrayscale(value);
    if (!cornerstoneRef.current) return;
    applyImageFilters(cornerstoneRef.current, brightness, contrast, value, invert, sharpness, gammaR, gammaG, gammaB);
  };

  const handleInvertChange = (value: boolean) => {
    setInvert(value);
    if (!cornerstoneRef.current) return;
    applyImageFilters(cornerstoneRef.current, brightness, contrast, grayscale, value, sharpness, gammaR, gammaG, gammaB);
  };

  const handleSharpnessChange = (value: number) => {
    setSharpness(value);
    applyFilters();
  };

  const handleGammaRChange = (value: number) => {
    setGammaR(value);
    applyFilters();
  };

  const handleGammaGChange = (value: number) => {
    setGammaG(value);
    applyFilters();
  };

  const handleGammaBChange = (value: number) => {
    setGammaB(value);
    applyFilters();
  };

  const handleRotate = () => {
    if (!cornerstoneRef.current) return;
    rotateDicomElement(cornerstoneRef.current);
  };

  const handleFlipHorizontal = () => {
    if (!cornerstoneRef.current) return;
    flipDicomElementHorizontal(cornerstoneRef.current);
  };

  const handleFlipVertical = () => {
    if (!cornerstoneRef.current) return;
    flipDicomElementVertical(cornerstoneRef.current);
  };



  const handleSave = async () => {
    const fabricCanvasData = fabricCanvas.current?.toJSON() || {};

    await saveFabricConfig(data.id, {
      brightness,
      contrast,
      grayscale,
      invert,
      sharpness,
      gammaR,
      gammaG,
      gammaB,
      annotations: fabricCanvasData,
    });
  };

  return (
    <div className="stack-viewer" id="stack-viewer-container">
      <div className="viewer-container">
        <div ref={cornerstoneRef} className="cornerstone-element" />
        <canvas ref={fabricRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onSave={handleSave}
        storageKey="fabric-annotations-stack"
      />
    </div>
  );
};

export default StackViewer;
