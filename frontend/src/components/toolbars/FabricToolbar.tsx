import { useEffect, useState } from 'react';
import { Canvas, Textbox, Rect, Line, Circle, Polygon } from 'fabric';
import { setDrawingMode, setupDrawingBrush } from '@/lib/fabric';
import { FaTrash, FaSave, FaDownload, FaPen, FaSun, FaAdjust, FaFont, FaPalette, FaExchangeAlt, FaSquare, FaMinus, FaCircle, FaDrawPolygon, FaRedo, FaArrowsAltH, FaArrowsAltV, FaMousePointer } from 'react-icons/fa';

interface FabricToolbarProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  onBrightnessChange: (value: number) => void;
  onContrastChange: (value: number) => void;
  onGrayscaleChange: (value: boolean) => void;
  onInvertChange: (value: boolean) => void;
  onRotate?: () => void;
  onFlipHorizontal?: () => void;
  onFlipVertical?: () => void;
  onSave?: () => void;
  storageKey: string;
}

const FabricToolbar: React.FC<FabricToolbarProps> = ({
  fabricCanvas,
  brightness,
  contrast,
  grayscale,
  invert,
  onBrightnessChange,
  onContrastChange,
  onGrayscaleChange,
  onInvertChange,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onSave,
  storageKey
}) => {
  const [isDrawingMode, setIsDrawingMode] = useState(false);
  const [isTextMode, setIsTextMode] = useState(false);
  const [isRectMode, setIsRectMode] = useState(false);
  const [isLineMode, setIsLineMode] = useState(false);
  const [isCircleMode, setIsCircleMode] = useState(false);
  const [isPolygonMode, setIsPolygonMode] = useState(false);
  const [isSelectMode, setIsSelectMode] = useState(true);

  useEffect(() => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    const mouseDownHandler = (e: any) => {
      if (isDrawingMode) {
        setupDrawingBrush(canvas);
      } else if (isTextMode && e.pointer) {
        const text = new Textbox('Text', {
          left: e.pointer.x,
          top: e.pointer.y,
          fontSize: 20,
          fill: 'red',
          fontFamily: 'Arial',
          width: 200
        });
        canvas.add(text);
        canvas.setActiveObject(text);
        text.enterEditing();
        canvas.renderAll();
        setIsTextMode(false);
      } else if (isRectMode && e.pointer) {
        const rect = new Rect({
          left: e.pointer.x,
          top: e.pointer.y,
          width: 100,
          height: 60,
          fill: 'transparent',
          stroke: 'red',
          strokeWidth: 2
        });
        canvas.add(rect);
        canvas.setActiveObject(rect);
        canvas.renderAll();
        setIsRectMode(false);
      } else if (isLineMode && e.pointer) {
        const line = new Line([e.pointer.x, e.pointer.y, e.pointer.x + 100, e.pointer.y], {
          stroke: 'red',
          strokeWidth: 2
        });
        canvas.add(line);
        canvas.setActiveObject(line);
        canvas.renderAll();
        setIsLineMode(false);
      } else if (isCircleMode && e.pointer) {
        const circle = new Circle({
          left: e.pointer.x,
          top: e.pointer.y,
          radius: 50,
          fill: 'transparent',
          stroke: 'red',
          strokeWidth: 2
        });
        canvas.add(circle);
        canvas.setActiveObject(circle);
        canvas.renderAll();
        setIsCircleMode(false);
      } else if (isPolygonMode && e.pointer) {
        const triangle = new Polygon([
          { x: e.pointer.x, y: e.pointer.y },
          { x: e.pointer.x + 50, y: e.pointer.y + 80 },
          { x: e.pointer.x - 50, y: e.pointer.y + 80 }
        ], {
          fill: 'transparent',
          stroke: 'red',
          strokeWidth: 2
        });
        canvas.add(triangle);
        canvas.setActiveObject(triangle);
        canvas.renderAll();
        setIsPolygonMode(false);
      }
    };

    const mouseUpHandler = () => {
      if (!isDrawingMode) canvas.isDrawingMode = false;
    };

    const disposeMouseDown = canvas.on('mouse:down', mouseDownHandler);
    const disposeMouseUp = canvas.on('mouse:up', mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseUp();
    };
  }, [fabricCanvas.current, isDrawingMode, isTextMode, isRectMode, isLineMode, isCircleMode, isPolygonMode]);



  useEffect(() => {
    if (!fabricCanvas.current) return;
    setDrawingMode(fabricCanvas.current, isDrawingMode);
  }, [isDrawingMode]);

  useEffect(() => {
    if (!fabricCanvas.current) return;
    if (isTextMode || isRectMode || isLineMode || isCircleMode || isPolygonMode) {
      fabricCanvas.current.defaultCursor = 'crosshair';
    } else {
      fabricCanvas.current.defaultCursor = 'default';
    }
  }, [isTextMode, isRectMode, isLineMode, isCircleMode, isPolygonMode]);



  const resetAllModes = () => {
    setIsDrawingMode(false);
    setIsTextMode(false);
    setIsRectMode(false);
    setIsLineMode(false);
    setIsCircleMode(false);
    setIsPolygonMode(false);
    setIsSelectMode(false);
  };

  const handleSelectMode = () => {
    resetAllModes();
    setIsSelectMode(true);
  };

  const handleFreehandToggle = () => {
    resetAllModes();
    setIsDrawingMode(true);
  };

  const handleTextToggle = () => {
    resetAllModes();
    setIsTextMode(true);
  };

  const handleRectToggle = () => {
    resetAllModes();
    setIsRectMode(true);
  };

  const handleLineToggle = () => {
    resetAllModes();
    setIsLineMode(true);
  };

  const handleCircleToggle = () => {
    resetAllModes();
    setIsCircleMode(true);
  };

  const handlePolygonToggle = () => {
    resetAllModes();
    setIsPolygonMode(true);
  };

  const handleRotate = () => {
    if (onRotate) {
      onRotate();
    }
  };

  const handleFlipHorizontal = () => {
    if (onFlipHorizontal) {
      onFlipHorizontal();
    }
  };

  const handleFlipVertical = () => {
    if (onFlipVertical) {
      onFlipVertical();
    }
  };

  const handleSave = () => {
    if (!fabricCanvas.current) return;
    const json = fabricCanvas.current.toJSON();
    localStorage.setItem(storageKey, JSON.stringify(json));
    if (onSave) onSave();
  };

  const handleExport = () => {
    if (!fabricCanvas.current) return;

    const imageSrc = fabricCanvas.current.toDataURL({
      format: 'png',
      multiplier: 1
    });

    const a = document.createElement('a');
    a.href = imageSrc;
    a.download = 'annotated-image.png';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleClear = () => {
    if (!fabricCanvas.current) return;
    fabricCanvas.current.clear();
  };

  return (
    <div className="fabric-toolbar-vertical">
      <div className="annotation-tools">
        <label>Tools</label>
        <div className="tool-grid">
          <button
            className={`tool-btn ${isSelectMode ? 'active' : ''}`}
            onClick={handleSelectMode}
            title="Select/Move objects"
          >
            <FaMousePointer />
          </button>
          <button
            className={`tool-btn ${isDrawingMode ? 'active' : ''}`}
            onClick={handleFreehandToggle}
            title="Draw freehand paths"
          >
            <FaPen />
          </button>
          <button
            className={`tool-btn ${isTextMode ? 'active' : ''}`}
            onClick={handleTextToggle}
            title="Add text"
          >
            <FaFont />
          </button>
          <button
            className={`tool-btn ${isRectMode ? 'active' : ''}`}
            onClick={handleRectToggle}
            title="Add rectangle"
          >
            <FaSquare />
          </button>
          <button
            className={`tool-btn ${isLineMode ? 'active' : ''}`}
            onClick={handleLineToggle}
            title="Add line"
          >
            <FaMinus />
          </button>
          <button
            className={`tool-btn ${isCircleMode ? 'active' : ''}`}
            onClick={handleCircleToggle}
            title="Add circle"
          >
            <FaCircle />
          </button>
          <button
            className={`tool-btn ${isPolygonMode ? 'active' : ''}`}
            onClick={handlePolygonToggle}
            title="Add triangle"
          >
            <FaDrawPolygon />
          </button>
          <button
            className="tool-btn"
            onClick={handleRotate}
            title="Rotate selected object"
          >
            <FaRedo />
          </button>
          <button
            className="tool-btn"
            onClick={handleFlipHorizontal}
            title="Flip horizontal"
          >
            <FaArrowsAltH />
          </button>
          <button
            className="tool-btn"
            onClick={handleFlipVertical}
            title="Flip vertical"
          >
            <FaArrowsAltV />
          </button>
          <button
            className={`tool-btn ${grayscale ? 'active' : ''}`}
            onClick={() => onGrayscaleChange(!grayscale)}
            title="Toggle grayscale"
          >
            <FaPalette />
          </button>
          <button
            className={`tool-btn ${invert ? 'active' : ''}`}
            onClick={() => onInvertChange(!invert)}
            title="Toggle invert"
          >
            <FaExchangeAlt />
          </button>
        </div>

        <div className="utility-tools">
          <button
            className="clear-btn"
            onClick={handleClear}
            title="Clear All"
          >
            <FaTrash /> Clear
          </button>
        </div>
      </div>

      <div className="toolbar-header">
        <h5>Adjust</h5>
      </div>

      <div className="control-item">
        <label><FaSun /> Bright</label>
        <input
          type="range"
          min="0.1"
          max="3"
          step="0.1"
          value={brightness}
          onChange={(e) => onBrightnessChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label><FaAdjust /> Contrast</label>
        <input
          type="range"
          min="0.1"
          max="3"
          step="0.1"
          value={contrast}
          onChange={(e) => onContrastChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <button
        className="save-btn"
        onClick={handleSave}
        title="Save Annotations & Settings"
      >
        <FaSave /> Save
      </button>

      <button
        className="save-btn export-btn"
        onClick={handleExport}
        title="Export Image"
      >
        <FaDownload /> Export
      </button>
    </div>
  );
};

export default FabricToolbar;
