import { useEffect, useState } from 'react';
import { Canvas, Textbox } from 'fabric';
import { setDrawingMode, setupDrawingBrush } from '@/lib/fabric';
import { FaTrash, FaSave, FaDownload, FaPen, FaSun, FaAdjust, FaFont, FaPalette, FaExchangeAlt } from 'react-icons/fa';

interface FabricToolbarProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  onBrightnessChange: (value: number) => void;
  onContrastChange: (value: number) => void;
  onGrayscaleChange: (value: boolean) => void;
  onInvertChange: (value: boolean) => void;
  onSave?: () => void;
  storageKey: string;
}

const FabricToolbar: React.FC<FabricToolbarProps> = ({
  fabricCanvas,
  brightness,
  contrast,
  grayscale,
  invert,
  onBrightnessChange,
  onContrastChange,
  onGrayscaleChange,
  onInvertChange,
  onSave,
  storageKey
}) => {
  const [isDrawingMode, setIsDrawingMode] = useState(false);
  const [isTextMode, setIsTextMode] = useState(false);

  useEffect(() => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    const mouseDownHandler = (e: any) => {
      if (isDrawingMode) {
        setupDrawingBrush(canvas);
      } else if (isTextMode && e.pointer) {
        const text = new Textbox('Text', {
          left: e.pointer.x,
          top: e.pointer.y,
          fontSize: 20,
          fill: 'red',
          fontFamily: 'Arial',
          width: 200
        });
        canvas.add(text);
        canvas.setActiveObject(text);
        text.enterEditing();
        canvas.renderAll();
        setIsTextMode(false);
      }
    };

    const mouseUpHandler = () => {
      if (!isDrawingMode) canvas.isDrawingMode = false;
    };

    const disposeMouseDown = canvas.on('mouse:down', mouseDownHandler);
    const disposeMouseUp = canvas.on('mouse:up', mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseUp();
    };
  }, [fabricCanvas.current, isDrawingMode, isTextMode]);



  useEffect(() => {
    if (!fabricCanvas.current) return;
    setDrawingMode(fabricCanvas.current, isDrawingMode);
  }, [isDrawingMode]);

  useEffect(() => {
    if (!fabricCanvas.current) return;
    if (isTextMode) {
      fabricCanvas.current.defaultCursor = 'crosshair';
    } else {
      fabricCanvas.current.defaultCursor = 'default';
    }
  }, [isTextMode]);



  const handleFreehandToggle = () => {
    setIsDrawingMode(!isDrawingMode);
    setIsTextMode(false);
  };

  const handleTextToggle = () => {
    setIsTextMode(!isTextMode);
    setIsDrawingMode(false);
  };

  const handleSave = () => {
    if (!fabricCanvas.current) return;
    const json = fabricCanvas.current.toJSON();
    localStorage.setItem(storageKey, JSON.stringify(json));
    if (onSave) onSave();
  };

  const handleExport = () => {
    if (!fabricCanvas.current) return;

    const imageSrc = fabricCanvas.current.toDataURL({
      format: 'png',
      multiplier: 1
    });

    const a = document.createElement('a');
    a.href = imageSrc;
    a.download = 'annotated-image.png';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleClear = () => {
    if (!fabricCanvas.current) return;
    fabricCanvas.current.clear();
  };

  return (
    <div className="fabric-toolbar-vertical">
      <div className="annotation-tools">
        <label>Tools</label>
        <div className="tool-grid">
          <button
            className={`tool-btn ${isDrawingMode ? 'active' : ''}`}
            onClick={handleFreehandToggle}
            title="Draw freehand paths"
          >
            <FaPen />
          </button>
          <button
            className={`tool-btn ${isTextMode ? 'active' : ''}`}
            onClick={handleTextToggle}
            title="Add text"
          >
            <FaFont />
          </button>
          <button
            className={`tool-btn ${grayscale ? 'active' : ''}`}
            onClick={() => onGrayscaleChange(!grayscale)}
            title="Toggle grayscale"
          >
            <FaPalette />
          </button>
          <button
            className={`tool-btn ${invert ? 'active' : ''}`}
            onClick={() => onInvertChange(!invert)}
            title="Toggle invert"
          >
            <FaExchangeAlt />
          </button>
        </div>

        <div className="utility-tools">
          <button
            className="clear-btn"
            onClick={handleClear}
            title="Clear All"
          >
            <FaTrash /> Clear
          </button>
        </div>
      </div>

      <div className="toolbar-header">
        <h5>Adjust</h5>
      </div>

      <div className="control-item">
        <label><FaSun /> Bright</label>
        <input
          type="range"
          min="0.1"
          max="3"
          step="0.1"
          value={brightness}
          onChange={(e) => onBrightnessChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label><FaAdjust /> Contrast</label>
        <input
          type="range"
          min="0.1"
          max="3"
          step="0.1"
          value={contrast}
          onChange={(e) => onContrastChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <button
        className="save-btn"
        onClick={handleSave}
        title="Save Annotations & Settings"
      >
        <FaSave /> Save
      </button>

      <button
        className="save-btn export-btn"
        onClick={handleExport}
        title="Export Image"
      >
        <FaDownload /> Export
      </button>
    </div>
  );
};

export default FabricToolbar;
