import {
  ZoomTool,
  PanTool,
  StackScrollTool,
  TrackballRotateTool,
  WindowLevelTool,
  OrientationMarkerTool,
  addTool,
  ToolGroupManager,
  Enums as csToolsEnums,
} from "@cornerstonejs/tools";
import vtkOrientationMarkerWidget from "@kitware/vtk.js/Interaction/Widgets/OrientationMarkerWidget";

export const { MouseBindings: mouseBindings } = csToolsEnums;

type ToolClass =
  | typeof ZoomTool
  | typeof PanTool
  | typeof StackScrollTool
  | typeof TrackballRotateTool
  | typeof WindowLevelTool;

type VolumeToolClass = ToolClass | typeof OrientationMarkerTool;

export interface ToolBinding {
  tool: ToolClass;
  bindings: { mouseButton: number }[];
}

export interface VolumeToolBinding {
  tool: VolumeToolClass;
  bindings: { mouseButton: number }[];
}

export interface ViewerConfig {
  tools: ToolClass[];
  bindings: ToolBinding[];
}

export interface VolumeViewerConfig {
  tools: VolumeToolClass[];
  bindings: VolumeToolBinding[];
}

const orientationMarkerConfig = {
  overlayMarkerType: OrientationMarkerTool.OVERLAY_MARKER_TYPES.ANNOTATED_CUBE,
  orientationWidget: {
    viewportCorner: vtkOrientationMarkerWidget.Corners.BOTTOM_LEFT,
  },
  overlayConfiguration: {
    [OrientationMarkerTool.OVERLAY_MARKER_TYPES.ANNOTATED_CUBE]: {
      faceProperties: {
        xPlus: {
          text: "L",
          faceColor: "#333333",
          fontColor: "white",
          faceRotation: 90,
          edgeColor: "#333333",
        },
        xMinus: {
          text: "R",
          faceColor: "#333333",
          fontColor: "white",
          faceRotation: 270,
          edgeColor: "#333333",
        },
        yPlus: {
          text: "P",
          faceColor: "#333333",
          fontColor: "white",
          faceRotation: 180,
          edgeColor: "#333333",
        },
        yMinus: {
          text: "A",
          faceColor: "#333333",
          fontColor: "white",
          edgeColor: "#333333",
        },
        zPlus: {
          text: "H",
          faceColor: "#333333",
          edgeColor: "#333333",
          fontColor: "white",
        },
        zMinus: {
          text: "F",
          faceColor: "#333333",
          edgeColor: "#333333",
          fontColor: "white",
          faceRotation: 180,
        },
      },
    },
  },
};

export function setupViewer(
  toolGroupId: string,
  viewportId: string,
  renderingEngineId: string,
  config: ViewerConfig | VolumeViewerConfig
) {
  config.tools.forEach((tool) => addTool(tool));

  let toolGroup = ToolGroupManager.getToolGroup(toolGroupId);
  if (!toolGroup) {
    toolGroup = ToolGroupManager.createToolGroup(toolGroupId)!;
  }
  toolGroup.addViewport(viewportId, renderingEngineId);

  config.tools.forEach((tool) => {
    if (tool === OrientationMarkerTool) {
      toolGroup.addTool(tool.toolName, orientationMarkerConfig);
      toolGroup.setToolEnabled(tool.toolName);
    } else {
      toolGroup.addTool(tool.toolName);
    }
  });

  config.bindings.forEach(({ tool, bindings }) => {
    toolGroup.setToolActive(tool.toolName, { bindings });
  });
}

export const volumeViewerConfig: VolumeViewerConfig = {
  tools: [ZoomTool, PanTool, TrackballRotateTool, OrientationMarkerTool],
  bindings: [
    {
      tool: TrackballRotateTool,
      bindings: [{ mouseButton: mouseBindings.Primary }],
    },
    {
      tool: ZoomTool,
      bindings: [{ mouseButton: mouseBindings.Wheel }],
    },
    {
      tool: PanTool,
      bindings: [{ mouseButton: mouseBindings.Auxiliary }],
    },
  ],
};

export const volume2dModeConfig: ViewerConfig = {
  tools: [ZoomTool, PanTool, StackScrollTool, WindowLevelTool],
  bindings: [
    {
      tool: WindowLevelTool,
      bindings: [{ mouseButton: mouseBindings.Primary }],
    },
    {
      tool: ZoomTool,
      bindings: [{ mouseButton: mouseBindings.Secondary }],
    },
    {
      tool: PanTool,
      bindings: [{ mouseButton: mouseBindings.Auxiliary }],
    },
    {
      tool: StackScrollTool,
      bindings: [{ mouseButton: mouseBindings.Wheel }],
    },
  ],
};


