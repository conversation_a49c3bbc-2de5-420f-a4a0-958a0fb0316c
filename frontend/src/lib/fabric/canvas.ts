import { Canvas, PencilBrush } from 'fabric';

export const DEFAULT_CANVAS_CONFIG = {
  selection: true,
  backgroundColor: 'transparent',
} as const;

export const DEFAULT_BRUSH_CONFIG = {
  color: 'red',
  width: 3,
} as const;

export const initializeFabricCanvas = async (
  canvasElement: HTMLCanvasElement,
  initialAnnotations?: any
): Promise<Canvas> => {
  canvasElement.setAttribute('data-fabric-canvas', 'true');

  const canvas = new Canvas(canvasElement, DEFAULT_CANVAS_CONFIG);

  if (initialAnnotations) {
    await loadCanvasAnnotations(canvas, initialAnnotations);
  }

  return canvas;
};

export const setupDrawingBrush = (canvas: Canvas): void => {
  canvas.freeDrawingBrush = new PencilBrush(canvas);
  canvas.freeDrawingBrush.color = DEFAULT_BRUSH_CONFIG.color;
  canvas.freeDrawingBrush.width = DEFAULT_BRUSH_CONFIG.width;
};

export const setDrawingMode = (canvas: Canvas, enabled: boolean): void => {
  canvas.isDrawingMode = enabled;
  if (enabled) setupDrawingBrush(canvas);
  canvas.renderAll();
};

export const loadCanvasAnnotations = async (canvas: Canvas, annotations: any): Promise<void> => {
  if (!annotations || !canvas) return;

  await canvas.loadFromJSON(annotations);
  canvas.calcOffset();
  canvas.renderAll();
};

export const setupImageCanvas = async (
  canvasElement: HTMLCanvasElement,
  imageUrl: string
): Promise<void> => {
  const ctx = canvasElement.getContext('2d');
  if (!ctx) return;

  const containerSize = canvasElement.parentElement?.clientWidth || 512;
  canvasElement.width = containerSize;
  canvasElement.height = containerSize;

  ctx.fillStyle = '#000';
  ctx.fillRect(0, 0, containerSize, containerSize);

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      const scale = Math.min(containerSize / img.width, containerSize / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const x = (containerSize - scaledWidth) / 2;
      const y = (containerSize - scaledHeight) / 2;

      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
      resolve();
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageUrl;
  });
};

export const disposeFabricCanvas = (fabricCanvas: Canvas | null): void => {
  if (!fabricCanvas) return;

  fabricCanvas.clear();
  fabricCanvas.dispose();
};
