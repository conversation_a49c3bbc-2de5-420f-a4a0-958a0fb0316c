import { Canvas, PencilBrush, FabricImage, filters } from 'fabric';

export const DEFAULT_CANVAS_CONFIG = {
  selection: true,
  backgroundColor: 'transparent',
} as const;

export const DEFAULT_BRUSH_CONFIG = {
  color: 'red',
  width: 3,
} as const;

export const initializeFabricCanvas = async (
  canvasElement: HTMLCanvasElement,
  initialAnnotations?: any
): Promise<Canvas> => {
  canvasElement.setAttribute('data-fabric-canvas', 'true');

  const canvas = new Canvas(canvasElement, DEFAULT_CANVAS_CONFIG);

  if (initialAnnotations) {
    await loadCanvasAnnotations(canvas, initialAnnotations);
  }

  return canvas;
};

export const setupDrawingBrush = (canvas: Canvas): void => {
  canvas.freeDrawingBrush = new PencilBrush(canvas);
  canvas.freeDrawingBrush.color = DEFAULT_BRUSH_CONFIG.color;
  canvas.freeDrawingBrush.width = DEFAULT_BRUSH_CONFIG.width;
};

export const setDrawingMode = (canvas: Canvas, enabled: boolean): void => {
  canvas.isDrawingMode = enabled;
  if (enabled) setupDrawingBrush(canvas);
  canvas.renderAll();
};

export const loadCanvasAnnotations = async (canvas: Canvas, annotations: any): Promise<void> => {
  if (!annotations || !canvas) return;

  await canvas.loadFromJSON(annotations);
  canvas.calcOffset();
  canvas.renderAll();
};

export const setupImageCanvas = async (
  canvasElement: HTMLCanvasElement,
  imageUrl: string
): Promise<void> => {
  const ctx = canvasElement.getContext('2d');
  if (!ctx) return;

  const containerSize = canvasElement.parentElement?.clientWidth || 512;
  canvasElement.width = containerSize;
  canvasElement.height = containerSize;

  ctx.fillStyle = '#000';
  ctx.fillRect(0, 0, containerSize, containerSize);

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      const scale = Math.min(containerSize / img.width, containerSize / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const x = (containerSize - scaledWidth) / 2;
      const y = (containerSize - scaledHeight) / 2;

      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
      resolve();
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageUrl;
  });
};

export const applyFiltersToCanvas = (
  canvas: Canvas,
  brightness: number,
  contrast: number,
  grayscale: boolean,
  invert: boolean,
  sharpness: number,
  gammaR: number,
  gammaG: number,
  gammaB: number,
): void => {
  const filterArray: any[] = [];

  if (brightness !== 1) {
    const filter = new filters.Brightness({
      brightness: brightness - 1
    });
    filterArray.push(filter);
  }

  if (contrast !== 1) {
    const filter = new filters.Contrast({
      contrast: contrast - 1
    });
    filterArray.push(filter);
  }

  if (grayscale) {
    const filter = new filters.Grayscale();
    filterArray.push(filter);
  }

  if (invert) {
    const filter = new filters.Invert();
    filterArray.push(filter);
  }

  if (sharpness !== 1) {
    const filter = new filters.Convolute({
      matrix: sharpness > 1 ?
        [0, -1, 0, -1, 5, -1, 0, -1, 0] :
        [1/9, 1/9, 1/9, 1/9, 1/9, 1/9, 1/9, 1/9, 1/9]
    });
    filterArray.push(filter);
  }

  if (gammaR !== 1 || gammaG !== 1 || gammaB !== 1) {
    const filter = new filters.Gamma({
      gamma: [1/gammaR, 1/gammaG, 1/gammaB]
    });
    filterArray.push(filter);
  }

  const objects = canvas.getObjects();
  objects.forEach(obj => {
    if (obj instanceof FabricImage) {
      obj.filters = [...filterArray];
      obj.applyFilters();
    }
  });

  if (canvas.backgroundImage && canvas.backgroundImage instanceof FabricImage) {
    canvas.backgroundImage.filters = [...filterArray];
    canvas.backgroundImage.applyFilters();
  }

  canvas.renderAll();
};

export const disposeFabricCanvas = (fabricCanvas: Canvas | null): void => {
  if (!fabricCanvas) return;

  fabricCanvas.clear();
  fabricCanvas.dispose();
};
