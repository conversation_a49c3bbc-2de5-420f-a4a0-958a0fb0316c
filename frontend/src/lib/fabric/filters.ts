export const applyImageFilters = (
  element: HTMLElement,
  brightness: number,
  contrast: number,
  grayscale: boolean = false,
  invert: boolean = false,
  sharpness: number = 1,
  gamma: number = 1,
): void => {
  const brightnessPercent = brightness * 100;
  const contrastPercent = contrast * 100;
  const grayscalePercent = grayscale ? 100 : 0;
  const invertPercent = invert ? 100 : 0;

  // Sharpness is implemented using CSS filter blur (inverted)
  const blurValue = sharpness < 1 ? (1 - sharpness) * 2 : 0;

  // Gamma correction using CSS filter
  const gammaFilter = gamma !== 1 ? `url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'><defs><filter id='gamma'><feComponentTransfer><feFuncR type='gamma' amplitude='1' exponent='${1/gamma}'/><feFuncG type='gamma' amplitude='1' exponent='${1/gamma}'/><feFuncB type='gamma' amplitude='1' exponent='${1/gamma}'/></feComponentTransfer></filter></defs></svg>#gamma")` : '';

  const filters = [
    `brightness(${brightnessPercent}%)`,
    `contrast(${contrastPercent}%)`,
    `grayscale(${grayscalePercent}%)`,
    `invert(${invertPercent}%)`,
    blurValue > 0 ? `blur(${blurValue}px)` : '',
    gammaFilter
  ].filter(f => f !== '').join(' ');

  element.style.filter = filters;
};
