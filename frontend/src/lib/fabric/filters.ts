export const applyImageFilters = (
  element: HTMLElement,
  brightness: number,
  contrast: number,
  grayscale: boolean = false,
  invert: boolean = false,
): void => {
  const brightnessPercent = brightness * 100;
  const contrastPercent = contrast * 100;
  const grayscalePercent = grayscale ? 100 : 0;
  const invertPercent = invert ? 100 : 0;

  element.style.filter = `brightness(${brightnessPercent}%) contrast(${contrastPercent}%) grayscale(${grayscalePercent}%) invert(${invertPercent}%)`;
};
