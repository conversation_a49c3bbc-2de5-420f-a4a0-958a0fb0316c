import { Canvas, filters, FabricImage } from 'fabric';

export const applyFabricFilters = (
  canvas: Canvas,
  brightness: number,
  contrast: number,
  grayscale: boolean,
  invert: boolean,
  sharpness: number,
  gammaR: number,
  gammaG: number,
  gammaB: number,
): void => {
  const filterArray: any[] = [];

  if (brightness !== 1) {
    const filter = new filters.Brightness({
      brightness: brightness - 1
    });
    filterArray.push(filter);
  }

  if (contrast !== 1) {
    const filter = new filters.Contrast({
      contrast: contrast - 1
    });
    filterArray.push(filter);
  }

  if (grayscale) {
    const filter = new filters.Grayscale();
    filterArray.push(filter);
  }

  if (invert) {
    const filter = new filters.Invert();
    filterArray.push(filter);
  }

  if (sharpness !== 1) {
    const filter = new filters.Convolute({
      matrix: sharpness > 1 ?
        [0, -1, 0, -1, 5, -1, 0, -1, 0] :
        [1/9, 1/9, 1/9, 1/9, 1/9, 1/9, 1/9, 1/9, 1/9]
    });
    filterArray.push(filter);
  }

  if (gammaR !== 1 || gammaG !== 1 || gammaB !== 1) {
    const filter = new filters.Gamma({
      gamma: [1/gammaR, 1/gammaG, 1/gammaB]
    });
    filterArray.push(filter);
  }

  const objects = canvas.getObjects();
  objects.forEach(obj => {
    if (obj instanceof FabricImage) {
      obj.filters = [...filterArray];
      obj.applyFilters();
    }
  });

  if (canvas.backgroundImage && canvas.backgroundImage instanceof FabricImage) {
    canvas.backgroundImage.filters = [...filterArray];
    canvas.backgroundImage.applyFilters();
  }

  canvas.renderAll();
};
