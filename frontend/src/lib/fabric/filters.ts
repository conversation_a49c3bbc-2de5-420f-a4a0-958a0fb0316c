export const applyImageFilters = (
  element: HTMLElement,
  brightness: number,
  contrast: number,
  grayscale: boolean,
  invert: boolean,
  sharpness: number,
): void => {
  const brightnessPercent = brightness * 100;
  const contrastPercent = contrast * 100;
  const grayscalePercent = grayscale ? 100 : 0;
  const invertPercent = invert ? 100 : 0;
  const blurValue = sharpness < 1 ? (1 - sharpness) * 2 : 0;

  const filterList = [
    `brightness(${brightnessPercent}%)`,
    `contrast(${contrastPercent}%)`,
    `grayscale(${grayscalePercent}%)`,
    `invert(${invertPercent}%)`,
    blurValue > 0 ? `blur(${blurValue}px)` : ''
  ].filter(f => f !== '').join(' ');

  element.style.filter = filterList;
};
