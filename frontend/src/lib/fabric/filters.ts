export const applyImageFilters = (
  element: HTMLElement,
  brightness: number,
  contrast: number,
  grayscale: boolean = false,
  invert: boolean = false,
  sharpness: number = 1,
  gammaR: number = 1,
  gammaG: number = 1,
  gammaB: number = 1,
): void => {
  const brightnessPercent = brightness * 100;
  const contrastPercent = contrast * 100;
  const grayscalePercent = grayscale ? 100 : 0;
  const invertPercent = invert ? 100 : 0;

  const blurValue = sharpness < 1 ? (1 - sharpness) * 2 : 0;

  const gammaFilter = (gammaR !== 1 || gammaG !== 1 || gammaB !== 1) ?
    `url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'><defs><filter id='gamma'><feComponentTransfer><feFuncR type='gamma' amplitude='1' exponent='${1/gammaR}'/><feFuncG type='gamma' amplitude='1' exponent='${1/gammaG}'/><feFuncB type='gamma' amplitude='1' exponent='${1/gammaB}'/></feComponentTransfer></filter></defs></svg>#gamma")` : '';

  const filters = [
    `brightness(${brightnessPercent}%)`,
    `contrast(${contrastPercent}%)`,
    `grayscale(${grayscalePercent}%)`,
    `invert(${invertPercent}%)`,
    blurValue > 0 ? `blur(${blurValue}px)` : '',
    gammaFilter
  ].filter(f => f !== '').join(' ');

  element.style.filter = filters;
};
