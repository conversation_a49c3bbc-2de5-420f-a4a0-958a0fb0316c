export const rotateImageCanvas = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const tempCanvas = document.createElement('canvas');
  const tempCtx = tempCanvas.getContext('2d');
  if (!tempCtx) return;

  tempCanvas.width = canvas.height;
  tempCanvas.height = canvas.width;

  tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2);
  tempCtx.rotate(Math.PI / 2);
  tempCtx.drawImage(canvas, -canvas.width / 2, -canvas.height / 2);

  canvas.width = tempCanvas.width;
  canvas.height = tempCanvas.height;
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(tempCanvas, 0, 0);
};

export const flipImageCanvasHorizontal = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const tempCanvas = document.createElement('canvas');
  const tempCtx = tempCanvas.getContext('2d');
  if (!tempCtx) return;

  tempCanvas.width = canvas.width;
  tempCanvas.height = canvas.height;

  tempCtx.translate(tempCanvas.width, 0);
  tempCtx.scale(-1, 1);
  tempCtx.drawImage(canvas, 0, 0);

  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(tempCanvas, 0, 0);
};

export const flipImageCanvasVertical = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const tempCanvas = document.createElement('canvas');
  const tempCtx = tempCanvas.getContext('2d');
  if (!tempCtx) return;

  tempCanvas.width = canvas.width;
  tempCanvas.height = canvas.height;

  tempCtx.translate(0, tempCanvas.height);
  tempCtx.scale(1, -1);
  tempCtx.drawImage(canvas, 0, 0);

  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(tempCanvas, 0, 0);
};

export const rotateDicomElement = (element: HTMLElement): void => {
  const currentTransform = element.style.transform || '';
  const rotateMatch = currentTransform.match(/rotate\((-?\d+)deg\)/);
  const currentRotation = rotateMatch ? parseInt(rotateMatch[1]) : 0;
  const newRotation = (currentRotation + 90) % 360;

  const otherTransforms = currentTransform.replace(/rotate\(-?\d+deg\)\s?/, '').trim();
  element.style.transform = `${otherTransforms} rotate(${newRotation}deg)`.trim();
};

export const flipDicomElementHorizontal = (element: HTMLElement): void => {
  const currentTransform = element.style.transform || '';
  const scaleMatch = currentTransform.match(/scaleX\((-?\d+(?:\.\d+)?)\)/);
  const currentScaleX = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
  const newScaleX = currentScaleX * -1;

  const otherTransforms = currentTransform.replace(/scaleX\(-?\d+(?:\.\d+)?\)\s?/, '').trim();
  element.style.transform = `${otherTransforms} scaleX(${newScaleX})`.trim();
};

export const flipDicomElementVertical = (element: HTMLElement): void => {
  const currentTransform = element.style.transform || '';
  const scaleMatch = currentTransform.match(/scaleY\((-?\d+(?:\.\d+)?)\)/);
  const currentScaleY = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
  const newScaleY = currentScaleY * -1;

  const otherTransforms = currentTransform.replace(/scaleY\(-?\d+(?:\.\d+)?\)\s?/, '').trim();
  element.style.transform = `${otherTransforms} scaleY(${newScaleY})`.trim();
};
