import { Canvas } from 'fabric';

export const rotateFabricCanvas = (canvas: Canvas): void => {
  if (canvas.backgroundImage) {
    // Store current background image properties
    const bgImage = canvas.backgroundImage;
    const currentAngle = bgImage.angle || 0;
    const newAngle = (currentAngle + 90) % 360;

    // Clear any existing background to prevent duplicates
    canvas.backgroundImage = null;

    // Apply rotation and set back
    bgImage.rotate(newAngle);
    canvas.backgroundImage = bgImage;

    // Force re-render
    canvas.requestRenderAll();
  }
};

export const flipFabricCanvasHorizontal = (canvas: Canvas): void => {
  // Flip background image
  if (canvas.backgroundImage) {
    const currentFlipX = canvas.backgroundImage.flipX || false;
    canvas.backgroundImage.set('flipX', !currentFlipX);
  }

  // Flip all objects (annotations)
  const objects = canvas.getObjects();
  objects.forEach(obj => {
    const currentFlipX = obj.flipX || false;
    obj.set('flipX', !currentFlipX);
    // Also flip the position horizontally
    obj.set('left', canvas.width! - obj.left! - obj.width! * obj.scaleX!);
  });

  canvas.requestRenderAll();
};

export const flipFabricCanvasVertical = (canvas: Canvas): void => {
  // Flip background image
  if (canvas.backgroundImage) {
    const currentFlipY = canvas.backgroundImage.flipY || false;
    canvas.backgroundImage.set('flipY', !currentFlipY);
  }

  // Flip all objects (annotations)
  const objects = canvas.getObjects();
  objects.forEach(obj => {
    const currentFlipY = obj.flipY || false;
    obj.set('flipY', !currentFlipY);
    // Also flip the position vertically
    obj.set('top', canvas.height! - obj.top! - obj.height! * obj.scaleY!);
  });

  canvas.requestRenderAll();
};

export const rotateDicomElement = (element: HTMLElement): void => {
  const currentTransform = element.style.transform || '';
  const rotateMatch = currentTransform.match(/rotate\((-?\d+)deg\)/);
  const currentRotation = rotateMatch ? parseInt(rotateMatch[1]) : 0;
  const newRotation = (currentRotation + 90) % 360;

  const otherTransforms = currentTransform.replace(/rotate\(-?\d+deg\)\s?/, '').trim();
  element.style.transform = `${otherTransforms} rotate(${newRotation}deg)`.trim();
};

export const flipDicomElementHorizontal = (element: HTMLElement): void => {
  const currentTransform = element.style.transform || '';
  const scaleMatch = currentTransform.match(/scaleX\((-?\d+(?:\.\d+)?)\)/);
  const currentScaleX = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
  const newScaleX = currentScaleX * -1;

  const otherTransforms = currentTransform.replace(/scaleX\(-?\d+(?:\.\d+)?\)\s?/, '').trim();
  element.style.transform = `${otherTransforms} scaleX(${newScaleX})`.trim();
};

export const flipDicomElementVertical = (element: HTMLElement): void => {
  const currentTransform = element.style.transform || '';
  const scaleMatch = currentTransform.match(/scaleY\((-?\d+(?:\.\d+)?)\)/);
  const currentScaleY = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
  const newScaleY = currentScaleY * -1;

  const otherTransforms = currentTransform.replace(/scaleY\(-?\d+(?:\.\d+)?\)\s?/, '').trim();
  element.style.transform = `${otherTransforms} scaleY(${newScaleY})`.trim();
};
