import { Canvas } from 'fabric';
import { initializeFabricCanvas, disposeFabricCanvas } from './canvas';

interface SetupFabricCanvasOptions {
  fabricCanvasElement: HTMLCanvasElement;
  fabricCanvasRef: React.MutableRefObject<Canvas | null>;
  annotations?: any;
  needsRenderDelay?: boolean; // For ImageViewer that needs requestAnimationFrame
}

export const setupFabricCanvas = async ({
  fabricCanvasElement,
  fabricCanvasRef,
  annotations,
  needsRenderDelay = false
}: SetupFabricCanvasOptions): Promise<void> => {
  if (!fabricCanvasElement) return;

  // Clean up existing canvas
  if (fabricCanvasRef.current) {
    disposeFabricCanvas(fabricCanvasRef.current);
    fabricCanvasRef.current = null;
  }

  // Wait for render frame if needed (ImageViewer case)
  if (needsRenderDelay) {
    await new Promise(resolve => requestAnimationFrame(resolve));
  }

  // Set canvas dimensions
  const containerSize = fabricCanvasElement.parentElement?.clientWidth || 512;
  fabricCanvasElement.width = containerSize;
  fabricCanvasElement.height = containerSize;
  fabricCanvasElement.removeAttribute('data-fabric');

  // Initialize fabric canvas with annotations
  fabricCanvasRef.current = await initializeFabricCanvas(
    fabricCanvasElement,
    annotations
  );
};
