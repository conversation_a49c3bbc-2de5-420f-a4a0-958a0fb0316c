import { Canvas } from 'fabric';
import { initializeFabricCanvas, disposeFabricCanvas } from './canvas';

interface SetupFabricCanvasOptions {
  fabricCanvasElement: HTMLCanvasElement;
  fabricCanvasRef: React.MutableRefObject<Canvas | null>;
  annotations?: any;
  needsRenderDelay?: boolean; // For ImageViewer that needs requestAnimationFrame
}

export const setupFabricCanvas = async ({
  fabricCanvasElement,
  fabricCanvasRef,
  annotations,
  needsRenderDelay = false
}: SetupFabricCanvasOptions): Promise<void> => {
  if (!fabricCanvasElement) return;

  console.log('setupFabricCanvas called with annotations:', annotations);

  // Skip if canvas is already initialized with the same element
  if (fabricCanvasRef.current && fabricCanvasRef.current.getElement() === fabricCanvasElement) {
    console.log('Canvas already initialized, skipping setup');
    return;
  }

  // Clean up existing canvas only if it's different
  if (fabricCanvasRef.current) {
    console.log('Disposing existing canvas');
    disposeFabricCanvas(fabricCanvasRef.current);
    fabricCanvasRef.current = null;
  }

  // Wait for render frame if needed (ImageViewer case)
  if (needsRenderDelay) {
    console.log('Waiting for render frame...');
    await new Promise(resolve => requestAnimationFrame(resolve));
  }

  // Set canvas dimensions
  const containerSize = fabricCanvasElement.parentElement?.clientWidth || 512;
  fabricCanvasElement.width = containerSize;
  fabricCanvasElement.height = containerSize;
  fabricCanvasElement.removeAttribute('data-fabric');

  console.log('Initializing fabric canvas...');
  // Initialize fabric canvas with annotations
  fabricCanvasRef.current = await initializeFabricCanvas(
    fabricCanvasElement,
    annotations
  );
  console.log('Fabric canvas initialized');
};
