import { Canvas } from 'fabric';
import { initializeFabricCanvas, disposeFabricCanvas } from './canvas';

interface SetupFabricCanvasOptions {
  fabricCanvasElement: HTMLCanvasElement;
  fabricCanvasRef: React.MutableRefObject<Canvas | null>;
  annotations?: any;
  needsRenderDelay?: boolean;
}

export const setupFabricCanvas = async ({
  fabricCanvasElement,
  fabricCanvasRef,
  annotations,
  needsRenderDelay = false
}: SetupFabricCanvasOptions): Promise<void> => {
  if (!fabricCanvasElement) return;

  if (fabricCanvasRef.current) {
    disposeFabricCanvas(fabricCanvasRef.current);
    fabricCanvasRef.current = null;
  }

  if (needsRenderDelay) {
    await new Promise(resolve => requestAnimationFrame(resolve));
  }

  const containerSize = fabricCanvasElement.parentElement?.clientWidth || 512;
  fabricCanvasElement.width = containerSize;
  fabricCanvasElement.height = containerSize;
  fabricCanvasElement.removeAttribute('data-fabric');

  fabricCanvasRef.current = await initializeFabricCanvas(
    fabricCanvasElement,
    annotations
  );
};
